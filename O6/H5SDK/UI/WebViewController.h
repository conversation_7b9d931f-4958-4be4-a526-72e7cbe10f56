//
//  WebViewController.h
//  H5SDK
//
//  Created by H5SDK on 2025/1/15.
//

#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface WebViewController : UIViewController <WKNavigationDelegate, WKUIDelegate, WKScriptMessageHandler>

// 初始化方法
- (instancetype)initWithURL:(NSString *)url;
- (instancetype)initWithURL:(NSString *)url initialParameters:(nullable NSDictionary *)parameters;

// WebView配置
@property (nonatomic, strong, readonly) WKWebView *webView;
@property (nonatomic, strong, readonly) UIProgressView *progressView;

// 页面控制
- (void)loadURL:(NSString *)url;
- (void)loadURL:(NSString *)url withParameters:(nullable NSDictionary *)parameters;
- (void)reload;

// 消息处理
- (void)registerScriptMessageHandler:(NSString *)name;
- (void)removeScriptMessageHandler:(NSString *)name;

// 页面状态
@property (nonatomic, assign, readonly) BOOL isLoading;

@end

NS_ASSUME_NONNULL_END
